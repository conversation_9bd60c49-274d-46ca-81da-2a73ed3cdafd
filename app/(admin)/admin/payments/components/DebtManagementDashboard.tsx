"use client";

import React, { useState, useEffect } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Search,
  Filter,
  Plus,
  DollarSign,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Clock,
  TrendingUp,
  TrendingDown,
  Users,
  FileText,
  Settings,
  Eye,
  Edit,
  Trash2,
  RefreshCw,
  Download,
  Upload,
  Target,
  Percent,
  Calendar,
  Receipt,
  CreditCard,
  PieChart,
  BarChart3,
  Activity,
  Zap,
  Shield,
} from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { toast } from "sonner";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Checkbox } from "@/components/ui/checkbox";

// Import our debt management actions
import {
  createManualDebtAction,
  adjustDebtAmountAction,
  forgiveDebtAction,
  restoreDebtAction,
  consolidateDebtsAction,
  bulkForgiveDebtsAction,
  getAssignmentDebtsAction,
  getDebtsSummaryAction,
  generateDebtReportAction,
  getDebtStatsAction,
  searchDebtsAction,
} from "@/actions/admin/debt-management";

// Import types
import { DebtRecord } from "@/types/payment-contract";

interface Assignment {
  id: string;
  driverName: string;
  vehicleName: string;
  vehicleRegistration: string;
  weeklyRate: number;
  outstandingBalance: number;
}

interface DebtManagementDashboardProps {
  assignments: Assignment[];
  onRefresh?: () => void;
}

export default function DebtManagementDashboard({
  assignments,
  onRefresh,
}: DebtManagementDashboardProps) {
  const [debts, setDebts] = useState<DebtRecord[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [filterStatus, setFilterStatus] = useState<string>("all");
  const [filterSourceType, setFilterSourceType] = useState<string>("all");
  const [selectedAssignment, setSelectedAssignment] = useState<string>("all");
  const [dateRange, setDateRange] = useState({ start: "", end: "" });

  // Dialog states
  const [showCreateDebtDialog, setShowCreateDebtDialog] = useState(false);
  const [showAdjustDebtDialog, setShowAdjustDebtDialog] = useState(false);
  const [showForgiveDebtDialog, setShowForgiveDebtDialog] = useState(false);
  const [showRestoreDebtDialog, setShowRestoreDebtDialog] = useState(false);
  const [showConsolidateDialog, setShowConsolidateDialog] = useState(false);
  const [showBulkForgiveDialog, setShowBulkForgiveDialog] = useState(false);
  const [showStatsDialog, setShowStatsDialog] = useState(false);
  const [showReportDialog, setShowReportDialog] = useState(false);
  const [selectedDebt, setSelectedDebt] = useState<DebtRecord | null>(null);

  // Selection states
  const [selectedDebts, setSelectedDebts] = useState<Set<number>>(new Set());
  const [selectAll, setSelectAll] = useState(false);

  // Form states
  const [createDebtFormData, setCreateDebtFormData] = useState({
    assignmentId: "",
    sourceType: "",
    debtAmount: "",
    description: "",
    notes: "",
  });

  const [adjustDebtFormData, setAdjustDebtFormData] = useState({
    newAmount: "",
    adjustmentReason: "",
    notes: "",
  });

  const [forgiveDebtFormData, setForgiveDebtFormData] = useState({
    forgivenessReason: "",
    notes: "",
  });

  const [restoreDebtFormData, setRestoreDebtFormData] = useState({
    restorationReason: "",
    notes: "",
  });

  const [consolidateFormData, setConsolidateFormData] = useState({
    assignmentId: "",
    consolidationNotes: "",
  });

  const [bulkForgiveFormData, setBulkForgiveFormData] = useState({
    forgivenessReason: "",
    notes: "",
  });

  // Stats state
  const [debtStats, setDebtStats] = useState({
    totalOutstandingDebt: "0",
    totalResolvedDebt: "0",
    outstandingDebtCount: 0,
    resolvedDebtCount: 0,
    averageDebtAmount: "0",
    resolutionRate: "0",
    lastDebtDate: "",
  });

  const [debtData, setDebtData] = useState({
    summary: [] as any[],
    totalRecords: 0,
    avgResolutionTime: "0",
    forgiveRate: "0",
  });

  // Load debt data
  useEffect(() => {
    loadDebtData();
    loadDebtStats();
  }, [assignments]);

  const loadDebtData = async () => {
    if (assignments.length === 0) return;

    setLoading(true);
    try {
      const assignmentIds = assignments.map(a => parseInt(a.id));
      
      const [summaryResult] = await Promise.all([
        getDebtsSummaryAction(assignmentIds),
      ]);

      if (summaryResult.success && summaryResult.data) {
        setDebtData({
          summary: summaryResult.data,
          totalRecords: summaryResult.data.length,
          avgResolutionTime: calculateAverageResolutionTime(summaryResult.data),
          forgiveRate: calculateForgiveRate(summaryResult.data),
        });
      }

      // Load detailed debts for all assignments
      const allDebts: DebtRecord[] = [];
      for (const assignmentId of assignmentIds) {
        const result = await getAssignmentDebtsAction(assignmentId, true); // Include resolved
        if (result.success && result.data) {
          allDebts.push(...result.data);
        }
      }
      
      setDebts(allDebts);
    } catch (error) {
      console.error("Error loading debt data:", error);
      toast.error("Failed to load debt data");
    } finally {
      setLoading(false);
    }
  };

  const loadDebtStats = async () => {
    if (assignments.length === 0) return;

    try {
      const assignmentIds = assignments.map(a => parseInt(a.id));
      const result = await getDebtStatsAction(assignmentIds);
      
      if (result.success && result.data) {
        setDebtStats({
          totalOutstandingDebt: result.data.totalOutstandingDebt,
          totalResolvedDebt: result.data.totalResolvedDebt,
          outstandingDebtCount: result.data.outstandingDebtCount,
          resolvedDebtCount: result.data.resolvedDebtCount,
          averageDebtAmount: result.data.averageDebtAmount,
          resolutionRate: result.data.resolutionRate,
          lastDebtDate: result.data.latestDebtDate || "",
        });
      }
    } catch (error) {
      console.error("Error loading debt stats:", error);
    }
  };

  const calculateAverageResolutionTime = (summaryData: any[]) => {
    // TODO: Calculate based on resolved debts
    return "7.2";
  };

  const calculateForgiveRate = (summaryData: any[]) => {
    // TODO: Calculate forgiveness rate from summary data
    return "15.8";
  };

  const handleCreateDebt = async () => {
    if (!createDebtFormData.assignmentId || !createDebtFormData.sourceType || !createDebtFormData.debtAmount || !createDebtFormData.description) {
      toast.error("Please fill in all required fields");
      return;
    }

    try {
      const formData = new FormData();
      formData.append("assignmentId", createDebtFormData.assignmentId);
      formData.append("sourceType", createDebtFormData.sourceType);
      formData.append("debtAmount", createDebtFormData.debtAmount);
      formData.append("description", createDebtFormData.description);
      formData.append("notes", createDebtFormData.notes);

      const result = await createManualDebtAction(null, formData);

      if (result.success) {
        toast.success("Debt created successfully");
        setShowCreateDebtDialog(false);
        setCreateDebtFormData({
          assignmentId: "",
          sourceType: "",
          debtAmount: "",
          description: "",
          notes: "",
        });
        loadDebtData();
        loadDebtStats();
        onRefresh?.();
      } else {
        toast.error(result.error || "Failed to create debt");
      }
    } catch (error) {
      console.error("Error creating debt:", error);
      toast.error("Failed to create debt");
    }
  };

  const handleAdjustDebt = async () => {
    if (!selectedDebt || !adjustDebtFormData.newAmount || !adjustDebtFormData.adjustmentReason.trim()) {
      toast.error("Please fill in all required fields");
      return;
    }

    try {
      const formData = new FormData();
      formData.append("debtId", selectedDebt.id.toString());
      formData.append("newAmount", adjustDebtFormData.newAmount);
      formData.append("adjustmentReason", adjustDebtFormData.adjustmentReason);
      formData.append("notes", adjustDebtFormData.notes);

      const result = await adjustDebtAmountAction(null, formData);

      if (result.success) {
        toast.success("Debt amount adjusted successfully");
        setShowAdjustDebtDialog(false);
        setSelectedDebt(null);
        setAdjustDebtFormData({
          newAmount: "",
          adjustmentReason: "",
          notes: "",
        });
        loadDebtData();
        loadDebtStats();
      } else {
        toast.error(result.error || "Failed to adjust debt amount");
      }
    } catch (error) {
      console.error("Error adjusting debt:", error);
      toast.error("Failed to adjust debt amount");
    }
  };

  const handleForgiveDebt = async () => {
    if (!selectedDebt || !forgiveDebtFormData.forgivenessReason.trim()) {
      toast.error("Please provide forgiveness reason");
      return;
    }

    try {
      const formData = new FormData();
      formData.append("debtId", selectedDebt.id.toString());
      formData.append("forgivenessReason", forgiveDebtFormData.forgivenessReason);
      formData.append("notes", forgiveDebtFormData.notes);

      const result = await forgiveDebtAction(null, formData);

      if (result.success) {
        toast.success("Debt forgiven successfully");
        setShowForgiveDebtDialog(false);
        setSelectedDebt(null);
        setForgiveDebtFormData({
          forgivenessReason: "",
          notes: "",
        });
        loadDebtData();
        loadDebtStats();
      } else {
        toast.error(result.error || "Failed to forgive debt");
      }
    } catch (error) {
      console.error("Error forgiving debt:", error);
      toast.error("Failed to forgive debt");
    }
  };

  const handleRestoreDebt = async () => {
    if (!selectedDebt || !restoreDebtFormData.restorationReason.trim()) {
      toast.error("Please provide restoration reason");
      return;
    }

    try {
      const formData = new FormData();
      formData.append("debtId", selectedDebt.id.toString());
      formData.append("restorationReason", restoreDebtFormData.restorationReason);
      formData.append("notes", restoreDebtFormData.notes);

      const result = await restoreDebtAction(null, formData);

      if (result.success) {
        toast.success("Debt restored successfully");
        setShowRestoreDebtDialog(false);
        setSelectedDebt(null);
        setRestoreDebtFormData({
          restorationReason: "",
          notes: "",
        });
        loadDebtData();
        loadDebtStats();
      } else {
        toast.error(result.error || "Failed to restore debt");
      }
    } catch (error) {
      console.error("Error restoring debt:", error);
      toast.error("Failed to restore debt");
    }
  };

  const handleConsolidateDebts = async () => {
    if (selectedDebts.size === 0 || !consolidateFormData.assignmentId) {
      toast.error("Please select debts and assignment for consolidation");
      return;
    }

    try {
      const formData = new FormData();
      formData.append("assignmentId", consolidateFormData.assignmentId);
      formData.append("debtIds", JSON.stringify(Array.from(selectedDebts)));
      formData.append("consolidationNotes", consolidateFormData.consolidationNotes);

      const result = await consolidateDebtsAction(null, formData);

      if (result.success) {
        toast.success(`${result.consolidatedCount || selectedDebts.size} debts consolidated successfully`);
        setShowConsolidateDialog(false);
        setSelectedDebts(new Set());
        setConsolidateFormData({
          assignmentId: "",
          consolidationNotes: "",
        });
        loadDebtData();
        loadDebtStats();
      } else {
        toast.error(result.error || "Failed to consolidate debts");
      }
    } catch (error) {
      console.error("Error consolidating debts:", error);
      toast.error("Failed to consolidate debts");
    }
  };

  const handleBulkForgive = async () => {
    if (selectedDebts.size === 0 || !bulkForgiveFormData.forgivenessReason.trim()) {
      toast.error("Please select debts and provide forgiveness reason");
      return;
    }

    try {
      const formData = new FormData();
      formData.append("debtIds", JSON.stringify(Array.from(selectedDebts)));
      formData.append("forgivenessReason", bulkForgiveFormData.forgivenessReason);
      formData.append("notes", bulkForgiveFormData.notes);

      const result = await bulkForgiveDebtsAction(null, formData);

      if (result.success && result.data) {
        toast.success(`${result.data.successful.length} debts forgiven successfully`);
        if (result.data.failed.length > 0) {
          toast.warning(`${result.data.failed.length} debts failed to forgive`);
        }
        setShowBulkForgiveDialog(false);
        setSelectedDebts(new Set());
        setBulkForgiveFormData({
          forgivenessReason: "",
          notes: "",
        });
        loadDebtData();
        loadDebtStats();
      } else {
        toast.error(result.error || "Failed to forgive debts");
      }
    } catch (error) {
      console.error("Error bulk forgiving debts:", error);
      toast.error("Failed to forgive debts");
    }
  };

  const handleGenerateReport = async () => {
    try {
      const assignmentIds = assignments
        .filter(a => selectedAssignment === "all" || a.id === selectedAssignment)
        .map(a => parseInt(a.id));

      const result = await generateDebtReportAction(assignmentIds, dateRange.start, dateRange.end);

      if (result.success && result.data) {
        // Handle report download/display
        toast.success("Debt report generated successfully");
        setShowReportDialog(false);
      } else {
        toast.error(result.error || "Failed to generate report");
      }
    } catch (error) {
      console.error("Error generating report:", error);
      toast.error("Failed to generate report");
    }
  };

  const openAdjustDialog = (debt: DebtRecord) => {
    setSelectedDebt(debt);
    setAdjustDebtFormData({
      newAmount: debt.outstandingAmount.toString(),
      adjustmentReason: "",
      notes: "",
    });
    setShowAdjustDebtDialog(true);
  };

  const openForgiveDialog = (debt: DebtRecord) => {
    setSelectedDebt(debt);
    setForgiveDebtFormData({
      forgivenessReason: "",
      notes: "",
    });
    setShowForgiveDebtDialog(true);
  };

  const openRestoreDialog = (debt: DebtRecord) => {
    setSelectedDebt(debt);
    setRestoreDebtFormData({
      restorationReason: "",
      notes: "",
    });
    setShowRestoreDebtDialog(true);
  };

  const handleSelectDebt = (debtId: number, checked: boolean) => {
    const newSelected = new Set(selectedDebts);
    if (checked) {
      newSelected.add(debtId);
    } else {
      newSelected.delete(debtId);
    }
    setSelectedDebts(newSelected);
    setSelectAll(newSelected.size === filteredDebts.length);
  };

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedDebts(new Set(filteredDebts.map(debt => debt.id)));
    } else {
      setSelectedDebts(new Set());
    }
    setSelectAll(checked);
  };

  // Filter debts based on search and filters
  const filteredDebts = debts.filter(debt => {
    const assignment = assignments.find(a => a.id === debt.assignmentId.toString());
    const matchesSearch = assignment?.driverName.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         debt.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         debt.sourceType.toLowerCase().includes(searchQuery.toLowerCase());
    
    const matchesStatus = filterStatus === "all" || 
                         (filterStatus === "outstanding" && !debt.isResolved) ||
                         (filterStatus === "resolved" && debt.isResolved);
    
    const matchesSourceType = filterSourceType === "all" || debt.sourceType === filterSourceType;
    
    const matchesAssignment = selectedAssignment === "all" || debt.assignmentId.toString() === selectedAssignment;
    
    const matchesDateRange = (!dateRange.start || debt.debtDate >= dateRange.start) &&
                            (!dateRange.end || debt.debtDate <= dateRange.end);
    
    return matchesSearch && matchesStatus && matchesSourceType && matchesAssignment && matchesDateRange;
  });

  const formatCurrency = (amount: number | string) => {
    const num = typeof amount === 'string' ? parseFloat(amount) : amount;
    return `R${num.toLocaleString()}`;
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  const getDebtStatusColor = (debt: DebtRecord) => {
    if (debt.isResolved) {
      if (debt.resolvedReason?.includes("forgiven")) {
        return "bg-blue-100 text-blue-800 border-blue-200";
      }
      return "bg-green-100 text-green-800 border-green-200";
    }
    return "bg-red-100 text-red-800 border-red-200";
  };

  const getDebtStatusIcon = (debt: DebtRecord) => {
    if (debt.isResolved) {
      if (debt.resolvedReason?.includes("forgiven")) {
        return <Shield size={16} className="text-blue-600" />;
      }
      return <CheckCircle size={16} className="text-green-600" />;
    }
    return <AlertTriangle size={16} className="text-red-600" />;
  };

  const getDebtStatusLabel = (debt: DebtRecord) => {
    if (debt.isResolved) {
      if (debt.resolvedReason?.includes("forgiven")) {
        return "Forgiven";
      }
      return "Resolved";
    }
    return "Outstanding";
  };

  const getSourceTypeColor = (sourceType: string) => {
    const colors = {
      earnings_shortfall: "bg-orange-100 text-orange-800",
      rate_adjustment: "bg-purple-100 text-purple-800",
      maintenance_cost: "bg-yellow-100 text-yellow-800",
      penalty: "bg-red-100 text-red-800",
      other: "bg-gray-100 text-gray-800",
    };
    return colors[sourceType as keyof typeof colors] || colors.other;
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-xl font-semibold">Debt Management Dashboard</h2>
          <p className="text-sm text-gray-500">Track, manage, and resolve driver debts with comprehensive analytics</p>
        </div>
        <div className="flex gap-2">
          <Button onClick={() => setShowStatsDialog(true)} variant="outline">
            <BarChart3 size={16} className="mr-2" />
            View Analytics
          </Button>
          <Button onClick={() => setShowReportDialog(true)} variant="outline">
            <Download size={16} className="mr-2" />
            Generate Report
          </Button>
          {selectedDebts.size > 0 && (
            <>
              <Button onClick={() => setShowConsolidateDialog(true)} variant="outline">
                <Target size={16} className="mr-2" />
                Consolidate ({selectedDebts.size})
              </Button>
              <Button onClick={() => setShowBulkForgiveDialog(true)} variant="outline">
                <Shield size={16} className="mr-2" />
                Bulk Forgive ({selectedDebts.size})
              </Button>
            </>
          )}
          <Button onClick={() => setShowCreateDebtDialog(true)} className="bg-[#009639] hover:bg-[#007A2F]">
            <Plus size={16} className="mr-2" />
            Create Debt
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-6 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center">
              <AlertTriangle className="h-8 w-8 text-red-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Outstanding Debt</p>
                <p className="text-2xl font-bold">{formatCurrency(debtStats.totalOutstandingDebt)}</p>
                <p className="text-xs text-gray-500">({debtStats.outstandingDebtCount} debts)</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center">
              <CheckCircle className="h-8 w-8 text-green-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Resolved Debt</p>
                <p className="text-2xl font-bold">{formatCurrency(debtStats.totalResolvedDebt)}</p>
                <p className="text-xs text-gray-500">({debtStats.resolvedDebtCount} debts)</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center">
              <Target className="h-8 w-8 text-blue-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Average Debt</p>
                <p className="text-2xl font-bold">{formatCurrency(debtStats.averageDebtAmount)}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center">
              <Percent className="h-8 w-8 text-purple-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Resolution Rate</p>
                <p className="text-2xl font-bold">{debtStats.resolutionRate}%</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center">
              <Clock className="h-8 w-8 text-orange-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Avg Resolution</p>
                <p className="text-2xl font-bold">{debtData.avgResolutionTime} days</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center">
              <Shield className="h-8 w-8 text-indigo-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Forgive Rate</p>
                <p className="text-2xl font-bold">{debtData.forgiveRate}%</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <div className="relative">
            <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
            <Input
              placeholder="Search debts..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-9 w-64"
            />
          </div>

          <Select value={filterStatus} onValueChange={setFilterStatus}>
            <SelectTrigger className="w-40">
              <SelectValue placeholder="Debt Status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Status</SelectItem>
              <SelectItem value="outstanding">Outstanding</SelectItem>
              <SelectItem value="resolved">Resolved</SelectItem>
            </SelectContent>
          </Select>

          <Select value={filterSourceType} onValueChange={setFilterSourceType}>
            <SelectTrigger className="w-44">
              <SelectValue placeholder="Source Type" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Sources</SelectItem>
              <SelectItem value="earnings_shortfall">Earnings Shortfall</SelectItem>
              <SelectItem value="rate_adjustment">Rate Adjustment</SelectItem>
              <SelectItem value="maintenance_cost">Maintenance Cost</SelectItem>
              <SelectItem value="penalty">Penalty</SelectItem>
              <SelectItem value="other">Other</SelectItem>
            </SelectContent>
          </Select>

          <Select value={selectedAssignment} onValueChange={setSelectedAssignment}>
            <SelectTrigger className="w-48">
              <SelectValue placeholder="Filter by Driver" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Drivers</SelectItem>
              {assignments.map(assignment => (
                <SelectItem key={assignment.id} value={assignment.id}>
                  {assignment.driverName}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <div className="flex gap-2">
            <Input
              type="date"
              value={dateRange.start}
              onChange={(e) => setDateRange(prev => ({ ...prev, start: e.target.value }))}
              className="w-36"
              placeholder="Start Date"
            />
            <Input
              type="date"
              value={dateRange.end}
              onChange={(e) => setDateRange(prev => ({ ...prev, end: e.target.value }))}
              className="w-36"
              placeholder="End Date"
            />
          </div>
        </div>

        <Button onClick={loadDebtData} variant="outline">
          <RefreshCw size={16} className="mr-2" />
          Refresh Data
        </Button>
      </div>

      {/* Debts Table */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>Debt Records</CardTitle>
            {filteredDebts.length > 0 && (
              <div className="flex items-center space-x-2">
                <Checkbox
                  checked={selectAll}
                  onCheckedChange={handleSelectAll}
                />
                <Label className="text-sm">Select All ({filteredDebts.length})</Label>
              </div>
            )}
          </div>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-10">
                  <Checkbox
                    checked={selectAll}
                    onCheckedChange={handleSelectAll}
                  />
                </TableHead>
                <TableHead>Driver</TableHead>
                <TableHead>Source Type</TableHead>
                <TableHead>Description</TableHead>
                <TableHead>Debt Amount</TableHead>
                <TableHead>Outstanding</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Date Created</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {loading ? (
                <TableRow>
                  <TableCell colSpan={9} className="text-center py-8">
                    <div className="flex items-center justify-center">
                      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#009639]"></div>
                      <span className="ml-2">Loading debts...</span>
                    </div>
                  </TableCell>
                </TableRow>
              ) : filteredDebts.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={9} className="text-center py-8">
                    <div className="text-gray-500">
                      <Receipt size={48} className="mx-auto mb-4 text-gray-300" />
                      <p>No debt records found</p>
                      <p className="text-sm">Create your first debt record to get started</p>
                    </div>
                  </TableCell>
                </TableRow>
              ) : (
                filteredDebts.map((debt) => {
                  const assignment = assignments.find(a => a.id === debt.assignmentId.toString());
                  return (
                    <TableRow key={debt.id}>
                      <TableCell>
                        <Checkbox
                          checked={selectedDebts.has(debt.id)}
                          onCheckedChange={(checked: boolean) => handleSelectDebt(debt.id, checked)}
                        />
                      </TableCell>
                      <TableCell>
                        <div>
                          <p className="font-medium">{assignment?.driverName || "Unknown"}</p>
                          <p className="text-sm text-gray-500">{assignment?.vehicleName || ""}</p>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant="outline" className={getSourceTypeColor(debt.sourceType)}>
                          {debt.sourceType.replace("_", " ").toUpperCase()}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="max-w-xs">
                          <p className="font-medium truncate">{debt.description}</p>
                          {debt.notes && (
                            <p className="text-sm text-gray-500 truncate">{debt.notes}</p>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        <span className="font-bold text-red-600">
                          {formatCurrency(debt.debtAmount)}
                        </span>
                      </TableCell>
                      <TableCell>
                        <span className="font-bold text-orange-600">
                          {formatCurrency(debt.outstandingAmount)}
                        </span>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          {getDebtStatusIcon(debt)}
                          <Badge variant="outline" className={getDebtStatusColor(debt)}>
                            {getDebtStatusLabel(debt)}
                          </Badge>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div>
                          <p className="font-medium">{formatDate(debt.debtDate)}</p>
                          {debt.resolvedAt && (
                            <p className="text-sm text-green-600">
                              Resolved: {formatDate(debt.resolvedAt)}
                            </p>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="sm">
                              <Settings size={16} />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem>
                              <Eye className="mr-2 h-4 w-4" />
                              View Details
                            </DropdownMenuItem>
                            {!debt.isResolved && (
                              <>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem onClick={() => openAdjustDialog(debt)}>
                                  <Edit className="mr-2 h-4 w-4" />
                                  Adjust Amount
                                </DropdownMenuItem>
                                <DropdownMenuItem onClick={() => openForgiveDialog(debt)}>
                                  <Shield className="mr-2 h-4 w-4 text-blue-600" />
                                  Forgive Debt
                                </DropdownMenuItem>
                              </>
                            )}
                            {debt.isResolved && debt.resolvedReason?.includes("forgiven") && (
                              <>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem onClick={() => openRestoreDialog(debt)}>
                                  <RefreshCw className="mr-2 h-4 w-4 text-orange-600" />
                                  Restore Debt
                                </DropdownMenuItem>
                              </>
                            )}
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  );
                })
              )}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Create Debt Dialog */}
      <Dialog open={showCreateDebtDialog} onOpenChange={setShowCreateDebtDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Create Manual Debt</DialogTitle>
            <DialogDescription>
              Create a new debt record for driver tracking and management
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="createAssignment">Assignment *</Label>
              <Select value={createDebtFormData.assignmentId} onValueChange={(value) => 
                setCreateDebtFormData(prev => ({ ...prev, assignmentId: value }))
              }>
                <SelectTrigger>
                  <SelectValue placeholder="Select assignment" />
                </SelectTrigger>
                <SelectContent>
                  {assignments.map(assignment => (
                    <SelectItem key={assignment.id} value={assignment.id}>
                      {assignment.driverName} - {assignment.vehicleName}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label htmlFor="createSourceType">Source Type *</Label>
              <Select value={createDebtFormData.sourceType} onValueChange={(value) => 
                setCreateDebtFormData(prev => ({ ...prev, sourceType: value }))
              }>
                <SelectTrigger>
                  <SelectValue placeholder="Select source type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="earnings_shortfall">Earnings Shortfall</SelectItem>
                  <SelectItem value="rate_adjustment">Rate Adjustment</SelectItem>
                  <SelectItem value="maintenance_cost">Maintenance Cost</SelectItem>
                  <SelectItem value="penalty">Penalty</SelectItem>
                  <SelectItem value="other">Other</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label htmlFor="createDebtAmount">Debt Amount (R) *</Label>
              <Input
                id="createDebtAmount"
                type="number"
                step="0.01"
                placeholder="0.00"
                value={createDebtFormData.debtAmount}
                onChange={(e) => setCreateDebtFormData(prev => ({ ...prev, debtAmount: e.target.value }))}
              />
            </div>
            <div>
              <Label htmlFor="createDescription">Description *</Label>
              <Input
                id="createDescription"
                placeholder="Brief description of the debt..."
                value={createDebtFormData.description}
                onChange={(e) => setCreateDebtFormData(prev => ({ ...prev, description: e.target.value }))}
              />
            </div>
            <div>
              <Label htmlFor="createNotes">Notes</Label>
              <Textarea
                id="createNotes"
                placeholder="Additional notes..."
                value={createDebtFormData.notes}
                onChange={(e) => setCreateDebtFormData(prev => ({ ...prev, notes: e.target.value }))}
                rows={3}
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowCreateDebtDialog(false)}>
              Cancel
            </Button>
            <Button onClick={handleCreateDebt} className="bg-[#009639] hover:bg-[#007A2F]">
              <Plus size={16} className="mr-2" />
              Create Debt
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Adjust Debt Dialog */}
      <Dialog open={showAdjustDebtDialog} onOpenChange={setShowAdjustDebtDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Adjust Debt Amount</DialogTitle>
            <DialogDescription>
              Modify the outstanding amount for this debt record
            </DialogDescription>
          </DialogHeader>
          {selectedDebt && (
            <div className="space-y-4">
              <div className="p-3 bg-gray-50 rounded">
                <p><strong>Current Amount:</strong> {formatCurrency(selectedDebt.outstandingAmount)}</p>
                <p><strong>Description:</strong> {selectedDebt.description}</p>
              </div>
              <div>
                <Label htmlFor="newAmount">New Amount (R) *</Label>
                <Input
                  id="newAmount"
                  type="number"
                  step="0.01"
                  placeholder="0.00"
                  value={adjustDebtFormData.newAmount}
                  onChange={(e) => setAdjustDebtFormData(prev => ({ ...prev, newAmount: e.target.value }))}
                />
              </div>
              <div>
                <Label htmlFor="adjustmentReason">Adjustment Reason *</Label>
                <Input
                  id="adjustmentReason"
                  placeholder="Reason for adjustment..."
                  value={adjustDebtFormData.adjustmentReason}
                  onChange={(e) => setAdjustDebtFormData(prev => ({ ...prev, adjustmentReason: e.target.value }))}
                />
              </div>
              <div>
                <Label htmlFor="adjustNotes">Notes</Label>
                <Textarea
                  id="adjustNotes"
                  placeholder="Additional notes..."
                  value={adjustDebtFormData.notes}
                  onChange={(e) => setAdjustDebtFormData(prev => ({ ...prev, notes: e.target.value }))}
                  rows={3}
                />
              </div>
            </div>
          )}
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowAdjustDebtDialog(false)}>
              Cancel
            </Button>
            <Button onClick={handleAdjustDebt} className="bg-blue-600 hover:bg-blue-700">
              <Edit size={16} className="mr-2" />
              Adjust Amount
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Forgive Debt Dialog */}
      <AlertDialog open={showForgiveDebtDialog} onOpenChange={setShowForgiveDebtDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Forgive Debt</AlertDialogTitle>
            <AlertDialogDescription>
              This will mark the debt as forgiven and remove it from outstanding balances.
              {selectedDebt && (
                <div className="mt-2 p-3 bg-gray-50 rounded">
                  <p><strong>Amount:</strong> {formatCurrency(selectedDebt.outstandingAmount)}</p>
                  <p><strong>Description:</strong> {selectedDebt.description}</p>
                </div>
              )}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <div className="my-4">
            <Label htmlFor="forgivenessReason">Forgiveness Reason *</Label>
            <Input
              id="forgivenessReason"
              placeholder="Reason for forgiving this debt..."
              value={forgiveDebtFormData.forgivenessReason}
              onChange={(e) => setForgiveDebtFormData(prev => ({ ...prev, forgivenessReason: e.target.value }))}
              className="mt-2"
            />
            <Label htmlFor="forgiveNotes" className="mt-4 block">Additional Notes</Label>
            <Textarea
              id="forgiveNotes"
              placeholder="Additional notes..."
              value={forgiveDebtFormData.notes}
              onChange={(e) => setForgiveDebtFormData(prev => ({ ...prev, notes: e.target.value }))}
              rows={3}
              className="mt-2"
            />
          </div>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleForgiveDebt}
              className="bg-blue-600 hover:bg-blue-700"
              disabled={!forgiveDebtFormData.forgivenessReason.trim()}
            >
              Forgive Debt
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Restore Debt Dialog */}
      <AlertDialog open={showRestoreDebtDialog} onOpenChange={setShowRestoreDebtDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Restore Debt</AlertDialogTitle>
            <AlertDialogDescription>
              This will restore a previously forgiven debt back to outstanding status.
              {selectedDebt && (
                <div className="mt-2 p-3 bg-gray-50 rounded">
                  <p><strong>Amount:</strong> {formatCurrency(selectedDebt.debtAmount)}</p>
                  <p><strong>Description:</strong> {selectedDebt.description}</p>
                  <p><strong>Originally Forgiven:</strong> {formatDate(selectedDebt.resolvedAt || "")}</p>
                </div>
              )}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <div className="my-4">
            <Label htmlFor="restorationReason">Restoration Reason *</Label>
            <Input
              id="restorationReason"
              placeholder="Reason for restoring this debt..."
              value={restoreDebtFormData.restorationReason}
              onChange={(e) => setRestoreDebtFormData(prev => ({ ...prev, restorationReason: e.target.value }))}
              className="mt-2"
            />
            <Label htmlFor="restoreNotes" className="mt-4 block">Additional Notes</Label>
            <Textarea
              id="restoreNotes"
              placeholder="Additional notes..."
              value={restoreDebtFormData.notes}
              onChange={(e) => setRestoreDebtFormData(prev => ({ ...prev, notes: e.target.value }))}
              rows={3}
              className="mt-2"
            />
          </div>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleRestoreDebt}
              className="bg-orange-600 hover:bg-orange-700"
              disabled={!restoreDebtFormData.restorationReason.trim()}
            >
              Restore Debt
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Consolidate Debts Dialog */}
      <Dialog open={showConsolidateDialog} onOpenChange={setShowConsolidateDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Consolidate Debts</DialogTitle>
            <DialogDescription>
              Combine selected debts ({selectedDebts.size}) into a single consolidated debt record
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="consolidateAssignment">Target Assignment *</Label>
              <Select value={consolidateFormData.assignmentId} onValueChange={(value) => 
                setConsolidateFormData(prev => ({ ...prev, assignmentId: value }))
              }>
                <SelectTrigger>
                  <SelectValue placeholder="Select assignment for consolidated debt" />
                </SelectTrigger>
                <SelectContent>
                  {assignments.map(assignment => (
                    <SelectItem key={assignment.id} value={assignment.id}>
                      {assignment.driverName} - {assignment.vehicleName}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label htmlFor="consolidationNotes">Consolidation Notes</Label>
              <Textarea
                id="consolidationNotes"
                placeholder="Notes about this debt consolidation..."
                value={consolidateFormData.consolidationNotes}
                onChange={(e) => setConsolidateFormData(prev => ({ ...prev, consolidationNotes: e.target.value }))}
                rows={3}
              />
            </div>
            <div className="p-3 bg-gray-50 rounded">
              <p><strong>Selected Debts:</strong> {selectedDebts.size}</p>
              <p><strong>Total Amount:</strong> {formatCurrency(
                filteredDebts
                  .filter(debt => selectedDebts.has(debt.id))
                  .reduce((sum, debt) => sum + debt.outstandingAmount, 0)
              )}</p>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowConsolidateDialog(false)}>
              Cancel
            </Button>
            <Button onClick={handleConsolidateDebts} className="bg-purple-600 hover:bg-purple-700">
              <Target size={16} className="mr-2" />
              Consolidate Debts
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Bulk Forgive Dialog */}
      <Dialog open={showBulkForgiveDialog} onOpenChange={setShowBulkForgiveDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Bulk Forgive Debts</DialogTitle>
            <DialogDescription>
              Forgive selected debts ({selectedDebts.size}) and remove them from outstanding balances
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="bulkForgivenessReason">Forgiveness Reason *</Label>
              <Input
                id="bulkForgivenessReason"
                placeholder="Reason for forgiving these debts..."
                value={bulkForgiveFormData.forgivenessReason}
                onChange={(e) => setBulkForgiveFormData(prev => ({ ...prev, forgivenessReason: e.target.value }))}
              />
            </div>
            <div>
              <Label htmlFor="bulkForgiveNotes">Additional Notes</Label>
              <Textarea
                id="bulkForgiveNotes"
                placeholder="Additional notes..."
                value={bulkForgiveFormData.notes}
                onChange={(e) => setBulkForgiveFormData(prev => ({ ...prev, notes: e.target.value }))}
                rows={3}
              />
            </div>
            <div className="p-3 bg-gray-50 rounded">
              <p><strong>Selected Debts:</strong> {selectedDebts.size}</p>
              <p><strong>Total Amount to Forgive:</strong> {formatCurrency(
                filteredDebts
                  .filter(debt => selectedDebts.has(debt.id))
                  .reduce((sum, debt) => sum + debt.outstandingAmount, 0)
              )}</p>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowBulkForgiveDialog(false)}>
              Cancel
            </Button>
            <Button 
              onClick={handleBulkForgive} 
              className="bg-blue-600 hover:bg-blue-700"
              disabled={!bulkForgiveFormData.forgivenessReason.trim()}
            >
              <Shield size={16} className="mr-2" />
              Forgive All Debts
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Stats Dialog */}
      <Dialog open={showStatsDialog} onOpenChange={setShowStatsDialog}>
        <DialogContent className="max-w-3xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Debt Analytics & Statistics</DialogTitle>
            <DialogDescription>
              Comprehensive debt management analytics and performance metrics
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-6">
            {/* Key Metrics */}
            <div className="grid grid-cols-3 gap-4">
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center">
                    <Activity className="h-8 w-8 text-green-600" />
                    <div className="ml-4">
                      <p className="text-sm font-medium text-gray-500">Resolution Efficiency</p>
                      <p className="text-xl font-bold">{debtStats.resolutionRate}%</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center">
                    <Clock className="h-8 w-8 text-blue-600" />
                    <div className="ml-4">
                      <p className="text-sm font-medium text-gray-500">Avg Resolution Time</p>
                      <p className="text-xl font-bold">{debtData.avgResolutionTime} days</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center">
                    <Shield className="h-8 w-8 text-purple-600" />
                    <div className="ml-4">
                      <p className="text-sm font-medium text-gray-500">Forgiveness Rate</p>
                      <p className="text-xl font-bold">{debtData.forgiveRate}%</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Status Distribution */}
            <div className="space-y-2">
              <h4 className="font-medium">Debt Status Distribution</h4>
              <div className="grid grid-cols-2 gap-2">
                {[
                  { 
                    status: "outstanding", 
                    count: debtStats.outstandingDebtCount, 
                    amount: debtStats.totalOutstandingDebt,
                    color: "text-red-600" 
                  },
                  { 
                    status: "resolved", 
                    count: debtStats.resolvedDebtCount, 
                    amount: debtStats.totalResolvedDebt,
                    color: "text-green-600" 
                  },
                ].map(({ status, count, amount, color }) => {
                  const totalCount = debtStats.outstandingDebtCount + debtStats.resolvedDebtCount;
                  const percentage = totalCount > 0 ? ((count / totalCount) * 100).toFixed(1) : "0";
                  return (
                    <div key={status} className="text-center p-4 border rounded">
                      <p className="text-xs uppercase font-medium">{status}</p>
                      <p className={`text-2xl font-bold ${color}`}>{count}</p>
                      <p className="text-sm text-gray-500">{percentage}%</p>
                      <p className="text-xs font-medium">{formatCurrency(amount)}</p>
                    </div>
                  );
                })}
              </div>
            </div>

            {/* Source Type Analysis */}
            <div className="space-y-2">
              <h4 className="font-medium">Debt Sources Analysis</h4>
              <div className="grid grid-cols-5 gap-2">
                {["earnings_shortfall", "rate_adjustment", "maintenance_cost", "penalty", "other"].map((sourceType) => {
                  const sourceDebts = debts.filter(debt => debt.sourceType === sourceType);
                  const sourceAmount = sourceDebts.reduce((sum, debt) => sum + debt.outstandingAmount, 0);
                  const sourcePercentage = debts.length > 0 ? ((sourceDebts.length / debts.length) * 100).toFixed(1) : "0";
                  
                  return (
                    <div key={sourceType} className="text-center p-3 border rounded">
                      <p className="text-xs uppercase font-medium truncate" title={sourceType.replace("_", " ")}>
                        {sourceType.replace("_", " ")}
                      </p>
                      <p className="text-lg font-bold text-orange-600">{sourceDebts.length}</p>
                      <p className="text-xs text-gray-500">{sourcePercentage}%</p>
                      <p className="text-xs font-medium">{formatCurrency(sourceAmount)}</p>
                    </div>
                  );
                })}
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button onClick={() => setShowStatsDialog(false)}>Close</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Report Dialog */}
      <Dialog open={showReportDialog} onOpenChange={setShowReportDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Generate Debt Report</DialogTitle>
            <DialogDescription>
              Generate comprehensive debt analysis report with filters
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="reportAssignment">Driver Selection</Label>
              <Select value={selectedAssignment} onValueChange={setSelectedAssignment}>
                <SelectTrigger>
                  <SelectValue placeholder="Select drivers for report" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Drivers</SelectItem>
                  {assignments.map(assignment => (
                    <SelectItem key={assignment.id} value={assignment.id}>
                      {assignment.driverName}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="reportStartDate">Start Date</Label>
                <Input
                  id="reportStartDate"
                  type="date"
                  value={dateRange.start}
                  onChange={(e) => setDateRange(prev => ({ ...prev, start: e.target.value }))}
                />
              </div>
              <div>
                <Label htmlFor="reportEndDate">End Date</Label>
                <Input
                  id="reportEndDate"
                  type="date"
                  value={dateRange.end}
                  onChange={(e) => setDateRange(prev => ({ ...prev, end: e.target.value }))}
                />
              </div>
            </div>
            <div className="p-3 bg-gray-50 rounded">
              <p><strong>Report Scope:</strong></p>
              <p>Drivers: {selectedAssignment === "all" ? "All" : assignments.find(a => a.id === selectedAssignment)?.driverName}</p>
              <p>Date Range: {dateRange.start || "All time"} to {dateRange.end || "Present"}</p>
              <p>Records: {filteredDebts.length} debt records</p>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowReportDialog(false)}>
              Cancel
            </Button>
            <Button onClick={handleGenerateReport} className="bg-green-600 hover:bg-green-700">
              <Download size={16} className="mr-2" />
              Generate Report
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
} 